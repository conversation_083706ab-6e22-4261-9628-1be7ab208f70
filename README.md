# Python Calculator with GUI

A fully functional calculator application built with Python's tkinter library, featuring a clean and intuitive graphical user interface.

## Features

### Core Functionality
- **Basic Arithmetic Operations**: Addition (+), Subtraction (-), Multiplication (×), Division (÷)
- **Decimal Support**: Handle decimal numbers with precision
- **Clear Function**: Reset calculator state with the 'C' button
- **Sign Toggle**: Change positive/negative numbers with the '±' button
- **Percentage**: Convert numbers to percentages with the '%' button

### User Interface
- **Clean Design**: Modern calculator layout with color-coded buttons
- **Large Display**: Easy-to-read black display with white text
- **Responsive Layout**: Buttons resize appropriately with the window
- **Visual Feedback**: Different colors for numbers (gray) and operations (orange)

### Advanced Features
- **Keyboard Support**: Use your keyboard for input
  - Number keys (0-9) for digits
  - +, -, *, / for operations
  - Enter or = for calculation
  - C for clear
  - Escape for clear
  - Backspace for deleting last digit
  - . for decimal point

- **Chain Operations**: Perform multiple operations in sequence
- **Error Handling**: 
  - Division by zero protection
  - Invalid input validation
  - User-friendly error messages

- **Proper Order of Operations**: Calculator follows standard mathematical precedence

## Requirements

- Python 3.x (tkinter is included with standard Python installation)
- No additional dependencies required

## Installation and Usage

1. **Clone or Download**: Save the `main.py` file to your desired directory

2. **Run the Calculator**:
   ```bash
   python main.py
   ```

3. **Using the Calculator**:
   - Click number buttons (0-9) to input numbers
   - Click operation buttons (+, -, ×, ÷) to select operations
   - Click '=' to calculate the result
   - Click 'C' to clear and start over
   - Use '±' to toggle between positive and negative numbers
   - Use '%' to convert the current number to a percentage

## Example Calculations

- **Basic Math**: `5 + 3 = 8`
- **Decimals**: `2.5 × 4 = 10`
- **Chain Operations**: `10 + 5 - 3 = 12`
- **Percentage**: `50% = 0.5`
- **Negative Numbers**: `±5 + 3 = -2`

## File Structure

```
calculator/
├── main.py              # Main calculator application
├── test_calculator.py   # Test script for validation
└── README.md           # This documentation
```

## Testing

Run the test script to verify calculator functionality:

```bash
python test_calculator.py
```

## Technical Details

### Architecture
- **Object-Oriented Design**: Calculator class encapsulates all functionality
- **Event-Driven**: Responds to both button clicks and keyboard events
- **State Management**: Maintains calculation state across operations

### Key Components
- **Display Management**: Updates the calculator display in real-time
- **Input Handling**: Processes both GUI and keyboard input
- **Calculation Engine**: Performs arithmetic operations with error handling
- **UI Layout**: Grid-based layout for responsive button arrangement

### Error Handling
- Division by zero detection and prevention
- Invalid input validation
- Graceful error recovery with user notifications
- Automatic calculator reset after errors

## Customization

The calculator can be easily customized by modifying:

- **Colors**: Change button and display colors in the `create_buttons()` and `create_display()` methods
- **Size**: Adjust window dimensions in the `__init__()` method
- **Layout**: Modify button arrangement in the `buttons` list
- **Functionality**: Add new operations by extending the calculation methods

## Troubleshooting

**Calculator won't start:**
- Ensure Python 3.x is installed
- Verify tkinter is available (usually included with Python)

**Display issues:**
- Check screen resolution and scaling settings
- Try running on a different display

**Keyboard input not working:**
- Click on the calculator window to ensure it has focus
- Try using mouse clicks instead

## License

This project is open source and available under the MIT License.
