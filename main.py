import tkinter as tk
from tkinter import messagebox

class Calculator:
    def __init__(self, root):
        self.root = root
        self.root.title("Python Calculator")
        self.root.geometry("300x400")
        self.root.resizable(False, False)

        # Variables to store calculation state
        self.current_input = ""
        self.total = 0
        self.operator = ""
        self.new_number = True

        # Create the display
        self.create_display()

        # Create the buttons
        self.create_buttons()

    def create_display(self):
        """Create the calculator display area"""
        # Display frame
        display_frame = tk.Frame(self.root, bg='black', padx=5, pady=5)
        display_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Display entry widget
        self.display = tk.Entry(
            display_frame,
            font=('Arial', 20, 'bold'),
            justify='right',
            bd=0,
            bg='black',
            fg='white',
            insertbackground='white',
            state='readonly'
        )
        self.display.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Initialize display
        self.update_display("0")

    def create_buttons(self):
        """Create all calculator buttons"""
        # Button frame
        button_frame = tk.Frame(self.root, bg='gray20')
        button_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Button configuration
        button_config = {
            'font': ('Arial', 14, 'bold'),
            'bd': 1,
            'relief': 'raised'
        }

        # Define button layout (text, row, col, color, command)
        buttons = [
            ('C', 0, 0, 'orange', self.clear),
            ('±', 0, 1, 'orange', self.toggle_sign),
            ('%', 0, 2, 'orange', self.percentage),
            ('÷', 0, 3, 'orange', lambda: self.set_operator('/')),

            ('7', 1, 0, 'gray60', lambda: self.add_digit('7')),
            ('8', 1, 1, 'gray60', lambda: self.add_digit('8')),
            ('9', 1, 2, 'gray60', lambda: self.add_digit('9')),
            ('×', 1, 3, 'orange', lambda: self.set_operator('*')),

            ('4', 2, 0, 'gray60', lambda: self.add_digit('4')),
            ('5', 2, 1, 'gray60', lambda: self.add_digit('5')),
            ('6', 2, 2, 'gray60', lambda: self.add_digit('6')),
            ('-', 2, 3, 'orange', lambda: self.set_operator('-')),

            ('1', 3, 0, 'gray60', lambda: self.add_digit('1')),
            ('2', 3, 1, 'gray60', lambda: self.add_digit('2')),
            ('3', 3, 2, 'gray60', lambda: self.add_digit('3')),
            ('+', 3, 3, 'orange', lambda: self.set_operator('+')),

            ('0', 4, 0, 'gray60', lambda: self.add_digit('0')),
            ('.', 4, 1, 'gray60', self.add_decimal),
            ('=', 4, 2, 'orange', self.calculate),
        ]

        # Create buttons
        for (text, row, col, color, command) in buttons:
            if text == '0':
                # Make 0 button span 2 columns
                btn = tk.Button(
                    button_frame,
                    text=text,
                    bg=color,
                    fg='white' if color == 'orange' else 'black',
                    command=command,
                    **button_config
                )
                btn.grid(row=row, column=col, columnspan=2, sticky='nsew', padx=1, pady=1)
            elif text == '=':
                # Make equals button span 2 columns
                btn = tk.Button(
                    button_frame,
                    text=text,
                    bg=color,
                    fg='white',
                    command=command,
                    **button_config
                )
                btn.grid(row=row, column=col, columnspan=2, sticky='nsew', padx=1, pady=1)
            else:
                btn = tk.Button(
                    button_frame,
                    text=text,
                    bg=color,
                    fg='white' if color == 'orange' else 'black',
                    command=command,
                    **button_config
                )
                btn.grid(row=row, column=col, sticky='nsew', padx=1, pady=1)

        # Configure grid weights for responsive layout
        for i in range(5):
            button_frame.grid_rowconfigure(i, weight=1)
        for i in range(4):
            button_frame.grid_columnconfigure(i, weight=1)

    def update_display(self, value):
        """Update the calculator display"""
        self.display.config(state='normal')
        self.display.delete(0, tk.END)
        self.display.insert(0, str(value))
        self.display.config(state='readonly')

    def add_digit(self, digit):
        """Add a digit to the current input"""
        if self.new_number:
            self.current_input = digit
            self.new_number = False
        else:
            if self.current_input == "0":
                self.current_input = digit
            else:
                self.current_input += digit

        self.update_display(self.current_input)

    def add_decimal(self):
        """Add decimal point to current input"""
        if self.new_number:
            self.current_input = "0."
            self.new_number = False
        elif '.' not in self.current_input:
            self.current_input += '.'

        self.update_display(self.current_input)

    def clear(self):
        """Clear all calculator state"""
        self.current_input = ""
        self.total = 0
        self.operator = ""
        self.new_number = True
        self.update_display("0")

    def toggle_sign(self):
        """Toggle the sign of the current number"""
        if self.current_input and self.current_input != "0":
            if self.current_input.startswith('-'):
                self.current_input = self.current_input[1:]
            else:
                self.current_input = '-' + self.current_input
            self.update_display(self.current_input)

    def percentage(self):
        """Convert current number to percentage"""
        if self.current_input:
            try:
                value = float(self.current_input) / 100
                self.current_input = str(value)
                self.update_display(self.current_input)
            except ValueError:
                self.show_error("Invalid input")

    def set_operator(self, op):
        """Set the current operator and prepare for next number"""
        if self.current_input:
            if self.operator and not self.new_number:
                # Chain operations
                self.calculate()
            else:
                try:
                    self.total = float(self.current_input)
                except ValueError:
                    self.show_error("Invalid input")
                    return

            self.operator = op
            self.new_number = True

    def calculate(self):
        """Perform the calculation"""
        if self.operator and self.current_input and not self.new_number:
            try:
                current_value = float(self.current_input)

                if self.operator == '+':
                    result = self.total + current_value
                elif self.operator == '-':
                    result = self.total - current_value
                elif self.operator == '*':
                    result = self.total * current_value
                elif self.operator == '/':
                    if current_value == 0:
                        self.show_error("Cannot divide by zero")
                        return
                    result = self.total / current_value
                else:
                    return

                # Format result to avoid floating point precision issues
                if result == int(result):
                    result = int(result)
                else:
                    result = round(result, 10)

                self.current_input = str(result)
                self.total = result
                self.operator = ""
                self.new_number = True
                self.update_display(self.current_input)

            except ValueError:
                self.show_error("Invalid input")
            except Exception as e:
                self.show_error(f"Error: {str(e)}")

    def show_error(self, message):
        """Show error message and reset calculator"""
        messagebox.showerror("Error", message)
        self.clear()

def main():
    """Main function to run the calculator"""
    root = tk.Tk()
    calculator = Calculator(root)

    # Bind keyboard events
    def on_key_press(event):
        key = event.char
        if key.isdigit():
            calculator.add_digit(key)
        elif key == '.':
            calculator.add_decimal()
        elif key in ['+', '-', '*', '/']:
            calculator.set_operator(key)
        elif key in ['\r', '\n', '=']:  # Enter key or equals
            calculator.calculate()
        elif key in ['c', 'C']:
            calculator.clear()
        elif event.keysym == 'Escape':
            calculator.clear()
        elif event.keysym == 'BackSpace':
            # Simple backspace functionality
            if calculator.current_input and not calculator.new_number:
                calculator.current_input = calculator.current_input[:-1]
                if not calculator.current_input:
                    calculator.current_input = "0"
                    calculator.new_number = True
                calculator.update_display(calculator.current_input)

    root.bind('<Key>', on_key_press)
    root.focus_set()  # Allow the window to receive key events

    # Center the window on screen
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
    y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
    root.geometry(f"+{x}+{y}")

    root.mainloop()

if __name__ == "__main__":
    main()