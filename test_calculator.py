#!/usr/bin/env python3
"""
Test script for the Calculator application.
This script tests the core calculation logic without the GUI.
"""

import sys
import os

# Add the current directory to the path to import the calculator
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the Calculator class
from main import Calculator
import tkinter as tk

def test_calculator_logic():
    """Test the calculator's core functionality"""
    print("Testing Calculator Logic...")
    
    # Create a test root window (won't be displayed)
    root = tk.Tk()
    root.withdraw()  # Hide the window
    
    # Create calculator instance
    calc = Calculator(root)
    
    # Test basic arithmetic
    test_cases = [
        # (description, operations, expected_result)
        ("Addition: 5 + 3", [
            ('add_digit', '5'),
            ('set_operator', '+'),
            ('add_digit', '3'),
            ('calculate', None)
        ], "8"),
        
        ("Subtraction: 10 - 4", [
            ('clear', None),
            ('add_digit', '1'),
            ('add_digit', '0'),
            ('set_operator', '-'),
            ('add_digit', '4'),
            ('calculate', None)
        ], "6"),
        
        ("Multiplication: 6 * 7", [
            ('clear', None),
            ('add_digit', '6'),
            ('set_operator', '*'),
            ('add_digit', '7'),
            ('calculate', None)
        ], "42"),
        
        ("Division: 15 / 3", [
            ('clear', None),
            ('add_digit', '1'),
            ('add_digit', '5'),
            ('set_operator', '/'),
            ('add_digit', '3'),
            ('calculate', None)
        ], "5"),
        
        ("Decimal: 2.5 + 1.5", [
            ('clear', None),
            ('add_digit', '2'),
            ('add_decimal', None),
            ('add_digit', '5'),
            ('set_operator', '+'),
            ('add_digit', '1'),
            ('add_decimal', None),
            ('add_digit', '5'),
            ('calculate', None)
        ], "4"),
        
        ("Percentage: 50%", [
            ('clear', None),
            ('add_digit', '5'),
            ('add_digit', '0'),
            ('percentage', None)
        ], "0.5"),
        
        ("Chain operations: 2 + 3 * 4", [
            ('clear', None),
            ('add_digit', '2'),
            ('set_operator', '+'),
            ('add_digit', '3'),
            ('set_operator', '*'),
            ('add_digit', '4'),
            ('calculate', None)
        ], "20"),  # Should be (2+3)*4 = 20 due to calculator logic
    ]
    
    passed = 0
    failed = 0
    
    for description, operations, expected in test_cases:
        try:
            # Execute operations
            for operation, param in operations:
                if operation == 'add_digit':
                    calc.add_digit(param)
                elif operation == 'set_operator':
                    calc.set_operator(param)
                elif operation == 'calculate':
                    calc.calculate()
                elif operation == 'clear':
                    calc.clear()
                elif operation == 'add_decimal':
                    calc.add_decimal()
                elif operation == 'percentage':
                    calc.percentage()
            
            # Get result from display
            result = calc.display.get()
            
            if result == expected:
                print(f"✓ PASS: {description} = {result}")
                passed += 1
            else:
                print(f"✗ FAIL: {description} = {result} (expected {expected})")
                failed += 1
                
        except Exception as e:
            print(f"✗ ERROR: {description} - {str(e)}")
            failed += 1
    
    print(f"\nTest Results: {passed} passed, {failed} failed")
    
    # Test error handling
    print("\nTesting Error Handling...")
    
    try:
        calc.clear()
        calc.add_digit('5')
        calc.set_operator('/')
        calc.add_digit('0')
        calc.calculate()
        print("✓ Division by zero handled correctly")
    except Exception as e:
        print(f"✗ Division by zero test failed: {e}")
    
    root.destroy()
    return passed, failed

if __name__ == "__main__":
    passed, failed = test_calculator_logic()
    if failed == 0:
        print("\n🎉 All tests passed! Calculator is working correctly.")
        sys.exit(0)
    else:
        print(f"\n⚠️  {failed} test(s) failed. Please check the implementation.")
        sys.exit(1)
