#!/usr/bin/env python3
"""
Simple validation script to check if the calculator can be imported and basic functions work.
"""

def validate_calculator():
    """Validate that the calculator module can be imported and basic functions exist."""
    print("Validating Calculator Application...")
    
    try:
        # Test import
        from main import Calculator
        print("✓ Calculator module imported successfully")
        
        # Test tkinter availability
        import tkinter as tk
        print("✓ tkinter library is available")
        
        # Test that Calculator class has required methods
        required_methods = [
            'add_digit', 'add_decimal', 'clear', 'set_operator', 
            'calculate', 'toggle_sign', 'percentage', 'update_display'
        ]
        
        for method in required_methods:
            if hasattr(Calculator, method):
                print(f"✓ Method '{method}' exists")
            else:
                print(f"✗ Method '{method}' missing")
                return False
        
        print("\n🎉 Calculator validation successful!")
        print("Run 'python main.py' to start the calculator application.")
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Validation error: {e}")
        return False

if __name__ == "__main__":
    success = validate_calculator()
    exit(0 if success else 1)
